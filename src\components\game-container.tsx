"use client";

import WaitingPage from "./waiting-page";
import React, { Suspense, useEffect, useState } from "react";
import BattlePage from "./battle-page";
import { createClient } from "../../supabase/client";
import { AudioPreloader } from "@/components/audio-preloader";
import { Volume2 } from "lucide-react";
import { User, Match, GameStateType, Difficulty, currentWord } from "@/interfaces/interfaces"; // Import the User and Match interfaces


interface GameContainerProps {
    roomId: string;
    roomName: string;
    roomColor: string;
    roomBgColor: string;
    initialTimeInSeconds: number;
    matchId?: string;
    currentUser: User; // Add currentUser to props
}

export default function GameContainer({
    roomId,
    roomName,
    roomColor,
    roomBgColor,
    initialTimeInSeconds,
    matchId,
    currentUser // Destructure currentUser
}: GameContainerProps) {

  const supabase = createClient();
  const [isAudioLoaded, setIsAudioLoaded] = useState(false);
  const [showAudioLoader, setShowAudioLoader] = useState(true);

  const [clientGameState, setClientGameState] = useState<GameStateType>("waiting");
  const [clientCurrentWordId, setClientCurrentWordId] = useState(0);
  const [currentWord, setCurrentWord] = useState<currentWord>({
    id: 0,
    text: "",
    difficulty: "easy",
    audioUrl: ""
  });

  // Check the game state
  useEffect(() => {
    if (!matchId) return; // Prevent fetch if no matchId

    const fetchGameState = async () => {
      try {
        const { data: matchData, error: matchError} = await supabase
          .from('matches')
          .select('current_state')
          .eq('id', matchId)
          .single()

        if (matchError) {
          console.error(matchError)
        }

        setClientGameState(matchData?.current_state)
      } catch (error) {
        console.error(error)
      }
    };

    fetchGameState();
  }, [matchId, supabase, currentUser.id]) // Add proper dependencies

  if (showAudioLoader) {
    return (
      <div className="flex items-center justify-center h-screen w-full bg-amber-50">
        <div className="w-full max-w-md p-6 bg-white border border-amber-200 rounded-xl shadow-md">
          <div className="flex items-center gap-3 mb-4">
            <div className="bg-amber-100 p-2 rounded-lg">
              <Volume2 size={20} className="text-amber-600" />
            </div>
            <div>
              <h3 className="text-base font-medium text-amber-900">
                Preparing Audio Files
              </h3>
              <p className="text-sm text-amber-600">
                Loading pronunciation files for {roomName} level
              </p>
            </div>
          </div>
          <AudioPreloader
            difficulty={roomName as Difficulty}
            onComplete={() => {
              setIsAudioLoaded(true);
              setTimeout(() => setShowAudioLoader(false), 2000);
            }}
            className="mb-3"
          />
          <p className="text-xs text-amber-600/80 mt-2">
            Loading audio files for a better game experience. This helps ensure smooth pronunciation during the battle.
          </p>
        </div>
      </div>
    );
  }

  return (
    <>
      {clientGameState === "waiting" ? (
        <WaitingPage 
          roomName={roomName}
          roomColor={roomColor}
          roomBgColor={roomBgColor}
          initialTimeInSeconds={initialTimeInSeconds}
          matchId={matchId}
          clientGameState={clientGameState}
          setClientGameState={setClientGameState}
          clientCurrentWordId={clientCurrentWordId}
          setClientCurrentWordId={setClientCurrentWordId}
          currentUser={currentUser} 
        />
      ) : (
        <Suspense fallback={<div>Loading...</div>}>
          <BattlePage
            roomName={roomName}
            roomColor={roomColor}
            roomBgColor={roomBgColor}
            matchId={matchId}
            clientGameState={clientGameState}
            setClientGameState={setClientGameState}
            clientCurrentWordId={clientCurrentWordId}
            setClientCurrentWordId={setClientCurrentWordId}
            currentWord={currentWord}
            setCurrentWord={setCurrentWord}
            currentUser={currentUser} 
          />
        </Suspense>
      )}
    </>
  )
}

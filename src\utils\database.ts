import { Difficulty, <PERSON> } from "@/interfaces/interfaces";
import { createClient } from "../../supabase/client";

const supabase = createClient();

// Interface for word data
export interface WordData {
  id: number;
  word: string;
  difficulty: Difficulty;
  audio_clip_url: string | null;
}

export async function getMatchCurrentState(matchId: string) {
    try {
        const { data, error } = await supabase
            .from('matches')
            .select('current_state')
            .eq('id', matchId)
            .single();

        if (error) {
          console.error('Error fetching match state:', error);
          return null;
        } else {
          return data.current_state;
        }

    } catch (error) {
        console.error('Error fetching match state:', error);
        return null;
    }
}

export async function updateMatchCurrentState(matchId: string, fromState: string, toState: string) {
  try {
    // Begin transaction to handle concurrent updates
    const { data: match, error: lockError } = await supabase
      .from('matches')
      .select('current_state')
      .eq('id', matchId)
      .single()
      .throwOnError();  // This ensures we fail fast if there's an error

    if (lockError) {
      console.error('Error acquiring lock:', lockError);
      return false;
    }

    if (match.current_state !== fromState) {
      return false;
    }

    // Perform the update with optimistic locking
    const { error: updateError } = await supabase
      .from('matches')
      .update({ current_state: toState })
      .eq('id', matchId)
      .eq('current_state', fromState) // Ensure state hasn't changed since we checked
      .single();

    if (updateError) {
      console.log("Someone else updated the match state, returning false");
      return false;
    }
    return true;
  } catch (error) {
    console.log("Someone else updated the match state, returning false");
    return false;
  }
}

export async function updateMatchState(matchId: string, fromState: string, toState: string) {
  try {
    const { error: updateError } = await supabase
      .from('matches')
      .update({ status: toState })
      .eq('id', matchId)
      .eq('status', fromState)
      .single();

    if (updateError) {
      console.log("Someone already updated match state")
      return;
    }

  } catch (error) {
    console.error("Error updating match state:", error);
  }
}

export async function updateMatchAllAvailableWordIds(matchId: string, allWordsIds: number[]) {
  try {
    const { error } = await supabase
      .from('matches')
      .update({ available_word_ids: allWordsIds })
      .eq('id', matchId);

    if (error) {
      console.error('Error updating available word IDs:', error);
      return false;
    }
  } catch (error) {
    console.error('Error updating available word IDs:', error);
    return false;
  }
}

export async function updateMatchCurrentWordId(matchId: string, wordId: number) {
  try {
    const { error } = await supabase
      .from('matches')
      .update({ current_word_id: wordId })
      .eq('id', matchId);

    if (error) {
      console.error('Error updating current word ID:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error updating current word ID:', error);
    return false;
  }
}

export async function setMatchLock(matchId: string, isLocked: boolean): Promise<boolean> {
  try {
    const { error } = await supabase
      .from('matches')
      .update({ is_locked: isLocked })
      .eq('id', matchId)
      .eq('is_locked', !isLocked)
      .single();

    if (error) {
      console.log("Someone already set the lock");
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error in setMatchLock:', error);
    return false;
  }
}

export async function setCurrentWordId(matchId: string, clientCurrentWordId: number) {
  try {
    // Begin transaction by acquiring lock
    const { data: match, error: lockError } = await supabase
      .from('matches')
      .select('difficulty, current_round, available_word_ids, current_word_id, previous_word_id, is_locked')
      .eq('id', matchId)
      .eq('is_locked', false) // Only proceed if not locked
      .single()
      .throwOnError();

    if (lockError || !match) {
      console.log('Match is locked or error acquiring lock:', lockError);
      return 0; // Return existing ID if locked
    }

    // Set lock
    await setMatchLock(matchId, true);

    try {
      let allWordsIds = match.available_word_ids || [];
      let pickedRandom = clientCurrentWordId;
      // If available_word_ids is empty, fetch all words for this difficulty
      if (allWordsIds.length === 0) {
        const { data } = await supabase
          .from('words')
          .select('id')
          .eq('difficulty', match.difficulty.toLowerCase());

        allWordsIds = data?.map((word) => word.id) || [];
        
        const { error } = await supabase
          .from('matches')
          .update({ available_word_ids: allWordsIds })
          .eq('id', matchId)
          .eq('is_locked', true); // Ensure we still have the lock

        if (error) {
          console.error('Error updating available word IDs:', error);
          return 0;
        }
      }

      // Randomize the current word ID and update the current word ID in the match
      const randomIndex = Math.floor(Math.random() * allWordsIds.length);
      pickedRandom = allWordsIds[randomIndex];
      await updateMatchCurrentWordId(matchId, pickedRandom);
      allWordsIds.splice(randomIndex, 1);
      await updateMatchAllAvailableWordIds(matchId, allWordsIds);
      
      return pickedRandom;
    } catch (error) {
      console.error('Error setting current word ID:', error);
      return 0; // Return existing ID on error
    }
  } catch (error) {
    console.log("Someone already updated the current word ID");
    return 0; // Return existing ID on error
  }
}

/**
 * Advanced version: Set current word ID with per-match tracking of used words
 * This requires a 'used_word_ids' JSONB field in the matches table
 */
export async function setCurrentWordIdAdvanced(matchId: string): Promise<boolean> {
  try {
    // Get the current match data including used words
    const { data: matchData, error: matchError } = await supabase
      .from('matches')
      .select('difficulty, current_round, used_word_ids')
      .eq('id', matchId)
      .single();

    if (matchError || !matchData) {
      console.error('Error fetching match data:', matchError);
      return false;
    }

    // Parse used word IDs (assuming it's stored as a JSON array)
    let usedWordIds: number[] = [];
    if (matchData.used_word_ids) {
      try {
        usedWordIds = Array.isArray(matchData.used_word_ids)
          ? matchData.used_word_ids
          : JSON.parse(matchData.used_word_ids);
      } catch (parseError) {
        console.warn('Error parsing used_word_ids, starting fresh:', parseError);
        usedWordIds = [];
      }
    }

    // // Get a random word ID excluding already used ones
    // const randomWordId = await getRandomWordId(matchData.difficulty, usedWordIds);

    // if (randomWordId === null) {
    //   console.error('Failed to get random word ID');
    //   return false;
    // }

    // // Add the new word ID to the used list
    // const updatedUsedWordIds = [...usedWordIds, randomWordId];

    // // Update the match with the new current word ID and updated used words list
    // const { error: updateError } = await supabase
    //   .from('matches')
    //   .update({
    //     current_word_id: randomWordId.toString(),
    //     used_word_ids: updatedUsedWordIds
    //   })
    //   .eq('id', matchId);

    // if (updateError) {
    //   console.error('Error updating current word ID and used words:', updateError);
    //   return false;
    // }

    // console.log(`Successfully set current word ID to ${randomWordId} for match ${matchId}. Used words: ${updatedUsedWordIds.length}`);
    return true;
  } catch (error) {
    console.error('Error setting current word ID (advanced):', error);
    return false;
  }
}

/**
 * Get the current word data for a match
 */
export async function getCurrentWord(matchId: string): Promise<WordData | null> {
  try {
    const { data: matchData, error: matchError } = await supabase
      .from('matches')
      .select('current_word_id')
      .eq('id', matchId)
      .single();

    if (matchError || !matchData || !matchData.current_word_id) {
      console.error('Error fetching match current word ID:', matchError);
      return null;
    }

    const { data: wordData, error: wordError } = await supabase
      .from('words')
      .select('id, word, difficulty, audio_clip_url')
      .eq('id', matchData.current_word_id)
      .single();

    if (wordError || !wordData) {
      console.error('Error fetching word data:', wordError);
      return null;
    }

    return wordData;
  } catch (error) {
    console.error('Error getting current word:', error);
    return null;
  }
}

/**
 * Get the total number of words available for a specific difficulty level
 * This can be used to determine the maximum number of rounds for a match
 */
export async function getWordCountForDifficulty(difficulty: Difficulty): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('words')
      .select('*', { count: 'exact', head: true })
      .eq('difficulty', difficulty.toLowerCase());

    if (error) {
      console.error('Error getting word count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error getting word count for difficulty:', error);
    return 0;
  }
}

/**
 * Advance to the next word in a match
 * This function sets a new random word and increments the round
 */
export async function advanceToNextWord(matchId: string): Promise<boolean> {
  try {
    // Get current match data
    const { data: matchData, error: matchError } = await supabase
      .from('matches')
      .select('current_round, difficulty')
      .eq('id', matchId)
      .single();

    if (matchError || !matchData) {
      console.error('Error fetching match data for advancement:', matchError);
      return false;
    }

    // Set new random word
    // const wordSet = await setCurrentWordId(matchId);
    // if (!wordSet) {
    //   console.error('Failed to set new word for next round');
    //   return false;
    // }

    // Increment the round
    const newRound = (matchData.current_round || 0) + 1;
    const { error: updateError } = await supabase
      .from('matches')
      .update({ current_round: newRound })
      .eq('id', matchId);

    if (updateError) {
      console.error('Error updating round number:', updateError);
      return false;
    }

    console.log(`Successfully advanced match ${matchId} to round ${newRound}`);
    return true;
  } catch (error) {
    console.error('Error advancing to next word:', error);
    return false;
  }
}